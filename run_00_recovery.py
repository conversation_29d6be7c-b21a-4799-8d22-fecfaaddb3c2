#!/usr/bin/env python3
"""
Аудитор целостности состояния системы обработки книг.

Этот скрипт НЕ "ремонтник", а "аудитор". Его задача — находить и устранять
несоответствия между состоянием задач в Redis и состоянием книг в PostgreSQL/S3,
которые возникают из-за сбоев воркеров.

Философия:
1. Безопасный откат, а не доделывание
2. Идемпотентность
3. Безопасность по умолчанию (--dry-run)
4. Четкое разделение ответственности

Архитектура:
- StaleTaskAuditor: Аудит зависших задач в Redis
- IncompleteBookAuditor: Аудит незавершенных книг в PostgreSQL + файловое хранилище
"""

import argparse
import logging
import sys
import time
from typing import Any

import psycopg
import redis
from app import settings
from app.processing.artifact_saver import _get_artifact_path
from app.storage.local import LocalStorageManager


class StaleTaskAuditor:
    """Аудитор зависших задач в Redis очередях.

    Фокус: Redis
    Проблема: Воркер умер, не успев завершить задачу. Задача "зависла" в очереди processing.
    Решение: Вернуть задачу в очередь new для повторной обработки.
    """

    def __init__(self, redis_client: redis.Redis, logger: logging.Logger):
        self.redis_client = redis_client
        self.logger = logger

    def find_stale_tasks(self) -> list[dict[str, Any]]:
        """Находит зависшие задачи в QUEUE_PARSING_PROCESSING.

        Returns:
            Список словарей зависших задач
        """
        stale_tasks = []
        current_time = time.time()

        try:
            # Получаем все задачи из очереди processing
            raw_tasks_result = self.redis_client.lrange(settings.QUEUE_PARSING_PROCESSING, 0, -1)
            raw_tasks = raw_tasks_result if isinstance(raw_tasks_result, list) else []
            self.logger.debug(f"Найдено {len(raw_tasks)} задач в очереди processing")

            for i, raw_task in enumerate(raw_tasks):
                try:
                    import json

                    task_data = json.loads(raw_task.decode("utf-8"))
                    claimed_at = task_data.get("_claimed_at", 0)

                    if claimed_at == 0:
                        self.logger.warning(
                            f"Задача #{i} без поля _claimed_at: {task_data.get('source_type', '?')}:{task_data.get('source_id', '?')}"
                        )
                        continue

                    # Проверяем, превышен ли таймаут
                    age_seconds = current_time - claimed_at
                    if age_seconds > settings.WORKER_TIMEOUT:
                        task_data["_raw_task"] = raw_task.decode("utf-8")
                        task_data["_age_seconds"] = age_seconds
                        stale_tasks.append(task_data)
                        self.logger.debug(
                            f"Зависшая задача: {task_data.get('source_type', '?')}:{task_data.get('source_id', '?')} (возраст: {age_seconds:.1f}s)"
                        )
                    else:
                        self.logger.debug(
                            f"Активная задача: {task_data.get('source_type', '?')}:{task_data.get('source_id', '?')} (возраст: {age_seconds:.1f}s)"
                        )

                except (json.JSONDecodeError, UnicodeDecodeError) as e:
                    self.logger.warning(f"Поврежденная задача #{i} в очереди processing: {e}")
                    continue

        except redis.RedisError as e:
            self.logger.error(f"Ошибка Redis при поиске зависших задач: {e}")

        total_tasks = len(raw_tasks) if "raw_tasks" in locals() else 0
        self.logger.info(f"Найдено зависших задач: {len(stale_tasks)} из {total_tasks}")
        return stale_tasks

    def recover(self, stale_tasks: list[dict[str, Any]], dry_run: bool) -> dict[str, int]:
        """Восстанавливает зависшие задачи.

        Args:
            stale_tasks: Список зависших задач
            dry_run: Режим только анализа

        Returns:
            Статистика: {'recovered': int, 'skipped_still_locked': int, 'errors': int}
        """
        stats = {"recovered": 0, "skipped_still_locked": 0, "errors": 0}

        for i, task_data in enumerate(stale_tasks, 1):
            try:
                source_type = task_data.get("source_type")
                source_id = task_data.get("source_id")
                age_seconds = task_data.get("_age_seconds", 0)

                if not source_type or not source_id:
                    self.logger.warning(f"Задача #{i} без source_type/source_id: {task_data}")
                    stats["errors"] += 1
                    continue

                # Проверяем блокировку
                lock_key = f"lock:book:{source_type}:{source_id}"

                try:
                    lock_exists = self.redis_client.exists(lock_key)
                    if lock_exists:
                        # Получаем TTL блокировки для диагностики
                        ttl_result = self.redis_client.ttl(lock_key)
                        ttl = ttl_result if isinstance(ttl_result, int) else -1
                        self.logger.info(
                            f"Задача {source_type}:{source_id} все еще заблокирована (TTL: {ttl}s), пропускаем"
                        )
                        stats["skipped_still_locked"] += 1
                        continue
                except redis.RedisError as e:
                    self.logger.error(f"Ошибка проверки блокировки {lock_key}: {e}")
                    stats["errors"] += 1
                    continue

                # Восстанавливаем задачу
                if dry_run:
                    self.logger.info(
                        f"[DRY-RUN] Планируется возврат задачи {source_type}:{source_id} в очередь new (возраст: {age_seconds:.1f}s)"
                    )
                else:
                    self._return_task_to_new(task_data)
                    self.logger.info(
                        f"✅ Задача {source_type}:{source_id} возвращена в очередь new (возраст: {age_seconds:.1f}s)"
                    )

                stats["recovered"] += 1

            except Exception as e:
                self.logger.error(f"Ошибка при восстановлении задачи #{i}: {e}", exc_info=True)
                stats["errors"] += 1

        return stats

    def _return_task_to_new(self, task_data: dict[str, Any]) -> None:
        """Атомарно возвращает задачу в очередь new."""
        # Убираем служебные поля
        clean_task = {k: v for k, v in task_data.items() if not k.startswith("_")}

        import json

        try:
            pipe = self.redis_client.pipeline()
            pipe.lrem(settings.QUEUE_PARSING_PROCESSING, 1, task_data["_raw_task"])
            pipe.lpush(settings.QUEUE_PARSING_NEW, json.dumps(clean_task))
            results = pipe.execute()

            # Проверяем результаты операций
            removed_count = results[0]  # Количество удаленных элементов из processing
            if removed_count == 0:
                self.logger.warning(
                    f"Задача не найдена в очереди processing: {task_data.get('source_type', '?')}:{task_data.get('source_id', '?')}"
                )

        except redis.RedisError as e:
            self.logger.error(f"Ошибка Redis при возврате задачи в очередь new: {e}")
            raise


class IncompleteBookAuditor:
    """Аудитор незавершенных книг в PostgreSQL + файловое хранилище.

    Фокус: PostgreSQL + Файловое хранилище
    Проблема: Воркер умер после сохранения метаданных в БД (status=10), но до создания артефакта.
    Решение: Полностью удалить все следы незавершенной транзакции.
    """

    def __init__(
        self,
        db_connection: psycopg.Connection,
        redis_client: redis.Redis,
        storage_manager: LocalStorageManager,
        logger: logging.Logger,
    ):
        self.db_connection = db_connection
        self.redis_client = redis_client
        self.storage_manager = storage_manager
        self.logger = logger

    def find_incomplete_books(self) -> list[dict[str, Any]]:
        """Находит книги со статусом 10 в PostgreSQL.

        Returns:
            Список словарей незавершенных книг
        """
        incomplete_books = []

        try:
            with self.db_connection.cursor() as cursor:
                # Получаем книги со статусом 10 и их источники
                cursor.execute("""
                    SELECT b.id, b.title, b.updated_at, bs.source_type, bs.source_id
                    FROM books b
                    LEFT JOIN book_sources bs ON b.id = bs.book_id
                    WHERE b.process_status = 10
                    ORDER BY b.updated_at DESC
                """)

                rows = cursor.fetchall()
                self.logger.debug(f"Найдено книг со статусом 10: {len(rows)}")

                for row in rows:
                    book_data = {
                        "id": str(row[0]),
                        "title": row[1] or "Без названия",
                        "updated_at": row[2],
                        "source_type": row[3],
                        "source_id": row[4],
                    }
                    incomplete_books.append(book_data)
                    self.logger.debug(f"Незавершенная книга: {book_data['id']} - {book_data['title'][:50]}...")

        except psycopg.Error as e:
            self.logger.error(f"Ошибка PostgreSQL при поиске незавершенных книг: {e}")

        self.logger.info(f"Найдено незавершенных книг: {len(incomplete_books)}")
        return incomplete_books

    def recover(self, incomplete_books: list[dict[str, Any]], dry_run: bool) -> dict[str, int]:
        """Восстанавливает незавершенные книги.

        Args:
            incomplete_books: Список незавершенных книг
            dry_run: Режим только анализа

        Returns:
            Статистика: {'rolled_back': int, 'status_updated': int, 'errors': int}
        """
        stats = {"rolled_back": 0, "status_updated": 0, "errors": 0}

        for i, book in enumerate(incomplete_books, 1):
            try:
                book_id = book["id"]
                book_title = book["title"][:50] + ("..." if len(book["title"]) > 50 else "")

                try:
                    artifact_path = _get_artifact_path(book_id)
                except ValueError as e:
                    self.logger.error(f"Некорректный book_id '{book_id}': {e}")
                    stats["errors"] += 1
                    continue

                if artifact_path.exists():
                    # Редкий случай: артефакт существует, обновляем статус
                    artifact_size = artifact_path.stat().st_size
                    if dry_run:
                        self.logger.info(
                            f"[DRY-RUN] Планируется обновить статус книги {book_id} на 20 (артефакт: {artifact_size} байт)"
                        )
                    else:
                        self._update_book_status(book_id, 20)
                        self.logger.info(
                            f"✅ Статус книги {book_id} обновлен на 20 - '{book_title}' (артефакт: {artifact_size} байт)"
                        )

                    stats["status_updated"] += 1
                else:
                    # Основной случай: полный откат
                    if dry_run:
                        self.logger.info(f"[DRY-RUN] Планируется полный откат книги {book_id} - '{book_title}'")
                    else:
                        self._rollback_book_completely(book)
                        self.logger.info(f"🗑️  Книга {book_id} полностью откачена - '{book_title}'")

                    stats["rolled_back"] += 1

            except Exception as e:
                book_id = book.get("id", "unknown")
                self.logger.error(f"Ошибка при восстановлении книги #{i} ({book_id}): {e}", exc_info=True)
                stats["errors"] += 1

        return stats

    def _update_book_status(self, book_id: str, status: int) -> None:
        """Обновляет статус книги в БД."""
        try:
            with self.db_connection.cursor() as cursor:
                cursor.execute(
                    "UPDATE books SET process_status = %s, updated_at = NOW() WHERE id = %s", (status, book_id)
                )
                affected_rows = cursor.rowcount

            self.db_connection.commit()

            if affected_rows == 0:
                self.logger.warning(f"Книга {book_id} не найдена в БД для обновления статуса")

        except psycopg.Error as e:
            self.logger.error(f"Ошибка PostgreSQL при обновлении статуса книги {book_id}: {e}")
            self.db_connection.rollback()
            raise

    def _rollback_book_completely(self, book: dict[str, Any]) -> None:
        """Полностью откатывает книгу из системы."""
        book_id = book["id"]
        source_type = book["source_type"]
        source_id = book["source_id"]

        try:
            # Удаляем из PostgreSQL (CASCADE удалит связанные записи)
            with self.db_connection.cursor() as cursor:
                cursor.execute("DELETE FROM books WHERE id = %s", (book_id,))
                deleted_rows = cursor.rowcount

            self.db_connection.commit()

            if deleted_rows == 0:
                self.logger.warning(f"Книга {book_id} не найдена в БД для удаления")
            else:
                self.logger.debug(f"Удалена книга {book_id} из PostgreSQL")

            # Удаляем из Redis SET_QUEUED_IDS
            if source_type and source_id:
                try:
                    removed_count_result = self.redis_client.srem(settings.SET_QUEUED_IDS, f"{source_type}:{source_id}")
                    removed_count = removed_count_result if isinstance(removed_count_result, int) else 0
                    if removed_count > 0:
                        self.logger.debug(f"Удален ключ {source_type}:{source_id} из SET_QUEUED_IDS")
                    else:
                        self.logger.debug(f"Ключ {source_type}:{source_id} не найден в SET_QUEUED_IDS")
                except redis.RedisError as e:
                    self.logger.error(f"Ошибка Redis при удалении ключа {source_type}:{source_id}: {e}")
                    # Не прерываем операцию, так как основная часть (PostgreSQL) уже выполнена
            else:
                self.logger.warning(f"Книга {book_id} без source_type/source_id, пропускаем очистку Redis")

        except psycopg.Error as e:
            self.logger.error(f"Ошибка PostgreSQL при удалении книги {book_id}: {e}")
            self.db_connection.rollback()
            raise


class SystemRecovery:
    """Главный координатор аудиторов целостности состояния."""

    def __init__(self, verbose: bool = False, debug: bool = False):
        self.verbose = verbose
        self.debug = debug
        self.logger = self._setup_logging()
        self.redis_client = redis.from_url(settings.REDIS_URL)
        self.db_connection = psycopg.connect(settings.DATABASE_URL)
        self.storage_manager = LocalStorageManager()

        # Инициализируем аудиторов
        self.stale_task_auditor = StaleTaskAuditor(self.redis_client, self.logger)
        self.incomplete_book_auditor = IncompleteBookAuditor(
            self.db_connection, self.redis_client, self.storage_manager, self.logger
        )

    def _setup_logging(self) -> logging.Logger:
        """Настраивает логирование в зависимости от уровня вербозности."""
        if self.debug:
            level = logging.DEBUG
        elif self.verbose:
            level = logging.INFO
        else:
            level = logging.WARNING

        logging.basicConfig(level=level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        return logging.getLogger(__name__)

    def run(self, skip_tasks: bool = False, skip_books: bool = False, dry_run: bool = True) -> None:
        """Запускает аудит целостности состояния.

        Args:
            skip_tasks: Пропустить аудит зависших задач
            skip_books: Пропустить аудит незавершенных книг
            dry_run: Режим только анализа
        """
        start_time = time.time()

        if self.verbose or self.debug:
            self.logger.info("🔍 Начинаем аудит целостности состояния системы")
            self.logger.info(f"⚙️  Параметры: skip_tasks={skip_tasks}, skip_books={skip_books}, dry_run={dry_run}")

            if dry_run:
                self.logger.info("⚠️  Режим DRY-RUN: изменения НЕ будут внесены")
            else:
                self.logger.info("🔧 Режим FIX: изменения БУДУТ внесены")

        total_stats = {}

        try:
            # Аудит зависших задач
            if not skip_tasks:
                if self.verbose or self.debug:
                    self.logger.info("\n" + "=" * 50)
                    self.logger.info("📋 ЭТАП 1: Аудит зависших задач в Redis")
                    self.logger.info("=" * 50)

                try:
                    stale_tasks = self.stale_task_auditor.find_stale_tasks()

                    if stale_tasks:
                        if self.verbose or self.debug:
                            self.logger.info(f"🔍 Найдено зависших задач: {len(stale_tasks)}")
                        task_stats = self.stale_task_auditor.recover(stale_tasks, dry_run)
                        total_stats["tasks"] = task_stats
                    else:
                        if self.verbose or self.debug:
                            self.logger.info("✅ Зависших задач не найдено")
                        total_stats["tasks"] = {"recovered": 0, "skipped_still_locked": 0, "errors": 0}

                except Exception as e:
                    self.logger.error(f"❌ Ошибка при аудите зависших задач: {e}", exc_info=True)
                    total_stats["tasks"] = {"recovered": 0, "skipped_still_locked": 0, "errors": 1}

            # Аудит незавершенных книг
            if not skip_books:
                if self.verbose or self.debug:
                    self.logger.info("\n" + "=" * 50)
                    self.logger.info("📚 ЭТАП 2: Аудит незавершенных книг в PostgreSQL")
                    self.logger.info("=" * 50)

                try:
                    incomplete_books = self.incomplete_book_auditor.find_incomplete_books()

                    if incomplete_books:
                        if self.verbose or self.debug:
                            self.logger.info(f"🔍 Найдено незавершенных книг: {len(incomplete_books)}")
                        book_stats = self.incomplete_book_auditor.recover(incomplete_books, dry_run)
                        total_stats["books"] = book_stats
                    else:
                        if self.verbose or self.debug:
                            self.logger.info("✅ Незавершенных книг не найдено")
                        total_stats["books"] = {"rolled_back": 0, "status_updated": 0, "errors": 0}

                except Exception as e:
                    self.logger.error(f"❌ Ошибка при аудите незавершенных книг: {e}", exc_info=True)
                    total_stats["books"] = {"rolled_back": 0, "status_updated": 0, "errors": 1}

            # Выводим отчет
            elapsed_time = time.time() - start_time
            self._print_report(total_stats, dry_run, elapsed_time)

        finally:
            # Закрываем соединения
            try:
                self.db_connection.close()
                self.redis_client.close()
                self.logger.debug("Соединения с БД и Redis закрыты")
            except Exception as e:
                self.logger.error(f"Ошибка при закрытии соединений: {e}")

    def _print_report(self, stats: dict[str, dict[str, int]], dry_run: bool, elapsed_time: float) -> None:
        """Выводит красивый отчет о проделанной работе."""
        mode = "DRY-RUN" if dry_run else "ВЫПОЛНЕНО"

        print(f"\n{'=' * 70}")
        print(f"📊 ОТЧЕТ АУДИТОРА ЦЕЛОСТНОСТИ СОСТОЯНИЯ ({mode})")
        print(f"{'=' * 70}")
        print(f"⏱️  Время выполнения: {elapsed_time:.2f} секунд")

        total_issues = 0
        total_fixed = 0
        total_errors = 0

        if "tasks" in stats:
            task_stats = stats["tasks"]
            print("\n📋 ЗАВИСШИЕ ЗАДАЧИ В REDIS:")
            print(f"   ✅ Возвращено в очередь new:     {task_stats['recovered']}")
            print(f"   ⏸️  Пропущено (заблокировано):   {task_stats['skipped_still_locked']}")
            print(f"   ❌ Ошибок обработки:            {task_stats['errors']}")

            task_issues = task_stats["recovered"] + task_stats["skipped_still_locked"]
            total_issues += task_issues
            total_fixed += task_stats["recovered"]
            total_errors += task_stats["errors"]

        if "books" in stats:
            book_stats = stats["books"]
            print("\n📚 НЕЗАВЕРШЕННЫЕ КНИГИ В POSTGRESQL:")
            print(f"   🗑️  Полностью откачено:          {book_stats['rolled_back']}")
            print(f"   ✅ Статус обновлен на 20:       {book_stats['status_updated']}")
            print(f"   ❌ Ошибок обработки:            {book_stats['errors']}")

            book_issues = book_stats["rolled_back"] + book_stats["status_updated"]
            total_issues += book_issues
            total_fixed += book_stats["rolled_back"] + book_stats["status_updated"]
            total_errors += book_stats["errors"]

        print(f"\n{'=' * 70}")
        print("📈 ИТОГОВАЯ СТАТИСТИКА:")
        print(f"   🔍 Всего проблем найдено:        {total_issues}")
        print(
            f"   {'🔧' if not dry_run else '📋'} {'Исправлено' if not dry_run else 'Планируется исправить'}: {total_fixed}"
        )
        print(f"   ❌ Всего ошибок:                 {total_errors}")

        if total_issues == 0:
            print("\n🎉 Система в порядке! Проблем не обнаружено.")
        elif dry_run and total_fixed > 0:
            print("\n⚠️  Для исправления проблем запустите скрипт с флагом --fix")
        elif not dry_run and total_fixed > 0:
            print("\n✅ Восстановление завершено успешно!")

        if total_errors > 0:
            print("\n⚠️  Обнаружены ошибки! Проверьте логи для подробностей.")

        print(f"{'=' * 70}")


def main():
    """Точка входа в программу."""
    parser = argparse.ArgumentParser(
        description="Аудитор целостности состояния системы обработки книг",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:
  %(prog)s                    # Анализ без изменений (минимальный вывод)
  %(prog)s --verbose          # Подробный анализ
  %(prog)s --fix              # Исправление найденных проблем
  %(prog)s --fix --verbose    # Исправление с подробным выводом
  %(prog)s --skip-tasks       # Пропустить аудит зависших задач
  %(prog)s --skip-books       # Пропустить аудит незавершенных книг
  %(prog)s --debug            # Максимально детальный вывод
        """,
    )

    parser.add_argument("--fix", action="store_true", help="Вносить изменения (по умолчанию: только анализ)")
    parser.add_argument("--skip-tasks", action="store_true", help="Пропустить аудит зависших задач в Redis")
    parser.add_argument("--skip-books", action="store_true", help="Пропустить аудит незавершенных книг в PostgreSQL")
    parser.add_argument("--verbose", "-v", action="store_true", help="Подробный вывод (этапы работы и детали)")
    parser.add_argument("--debug", action="store_true", help="Максимально детальный вывод для отладки")

    args = parser.parse_args()

    try:
        recovery = SystemRecovery(verbose=args.verbose, debug=args.debug)
        recovery.run(skip_tasks=args.skip_tasks, skip_books=args.skip_books, dry_run=not args.fix)
    except KeyboardInterrupt:
        print("\n⚠️  Операция прервана пользователем")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Критическая ошибка: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
