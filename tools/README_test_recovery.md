# Тесты для run_00_recovery.py

## Описание

Интеграционные тесты для аудитора целостности состояния системы обработки книг.

## Структура тестов

### TestStaleTaskAuditor
Тесты для аудитора зависших задач в Redis:

- `test_find_stale_tasks_empty_queue` - поиск в пустой очереди
- `test_find_stale_tasks_with_fresh_tasks` - поиск со свежими задачами
- `test_find_stale_tasks_with_stale_tasks` - поиск с зависшими задачами
- `test_recover_stale_task_dry_run` - восстановление в режиме dry-run
- `test_recover_stale_task_fix_mode` - восстановление в режиме fix
- `test_recover_locked_task` - пропуск заблокированных задач

### TestIncompleteBookAuditor
Тесты для аудитора незавершенных книг в PostgreSQL:

- `test_find_incomplete_books_empty` - поиск в пустой БД
- `test_find_incomplete_books_with_status_10` - поиск книг со статусом 10
- `test_recover_book_with_artifact_dry_run` - восстановление с артефактом (dry-run)
- `test_recover_book_without_artifact_dry_run` - восстановление без артефакта (dry-run)
- `test_recover_book_without_artifact_fix_mode` - полный откат в режиме fix

### TestSystemRecoveryIntegration
Интеграционные тесты для SystemRecovery:

- `test_system_recovery_dry_run_no_issues` - полный цикл без проблем

## Технические детали

### Моки и заглушки

- **FakeRedis**: Используется `fakeredis` для эмуляции Redis
- **MockDBConnection**: Адаптер для SQLite, совместимый с PostgreSQL API
- **MockCursor**: Курсор с автоматическим преобразованием `%s` → `?`

### Зависимости

```bash
pip install fakeredis
```

## Запуск тестов

```bash
# Запуск всех тестов
python tools/test_recovery.py

# Запуск с подробным выводом
python tools/test_recovery.py -v

# Запуск конкретного теста
python -m unittest tools.test_recovery.TestStaleTaskAuditor.test_find_stale_tasks_empty_queue
```

## Покрытие

Тесты покрывают все основные сценарии:

✅ Поиск зависших задач в Redis  
✅ Восстановление зависших задач  
✅ Обработка заблокированных задач  
✅ Поиск незавершенных книг в PostgreSQL  
✅ Откат незавершенных книг  
✅ Обновление статуса при наличии артефакта  
✅ Режимы dry-run и fix  
✅ Обработка ошибок  

## Примеры тестовых сценариев

### Сценарий 1: Зависшая задача
```python
# Задача захвачена час назад, блокировки нет
stale_task = {
    'source_type': 1,
    'source_id': 12345,
    '_claimed_at': time.time() - 3600
}
# Ожидается: возврат в очередь new
```

### Сценарий 2: Незавершенная книга
```python
# Книга со статусом 10, артефакта нет
book = {
    'id': 'uuid-book-id',
    'process_status': 10
}
# Ожидается: полное удаление из системы
```

### Сценарий 3: Книга с артефактом
```python
# Книга со статусом 10, артефакт существует
book = {
    'id': 'uuid-book-id', 
    'process_status': 10
}
# artifact_path.exists() = True
# Ожидается: обновление статуса на 20
```
