#!/usr/bin/env python3
"""
Интеграционные тесты для run_00_recovery.py

Тестирует все сценарии работы аудиторов целостности состояния:
1. Зависшие задачи в Redis
2. Незавершенные книги в PostgreSQL
3. Режимы dry-run и fix
"""

import json
import logging
import sqlite3

# Добавляем корневую директорию в путь для импорта
import sys
import tempfile
import time
import unittest
from pathlib import Path
from unittest.mock import Mock, patch
from uuid import uuid4

import fakeredis

sys.path.insert(0, str(Path(__file__).parent.parent))

from app import settings
from run_00_recovery import IncompleteBookAuditor, StaleTaskAuditor, SystemRecovery


class TestStaleTaskAuditor(unittest.TestCase):
    """Тесты для аудитора зависших задач."""

    def setUp(self):
        """Настройка тестового окружения."""
        self.fake_redis = fakeredis.FakeRedis()
        self.logger = logging.getLogger("test")
        self.auditor = StaleTaskAuditor(self.fake_redis, self.logger)

        # Мокаем настройки
        self.original_timeout = settings.WORKER_TIMEOUT
        settings.WORKER_TIMEOUT = 300  # 5 минут

    def tearDown(self):
        """Очистка после тестов."""
        settings.WORKER_TIMEOUT = self.original_timeout
        self.fake_redis.flushall()

    def test_find_stale_tasks_empty_queue(self):
        """Тест поиска зависших задач в пустой очереди."""
        stale_tasks = self.auditor.find_stale_tasks()
        self.assertEqual(len(stale_tasks), 0)

    def test_find_stale_tasks_with_fresh_tasks(self):
        """Тест поиска зависших задач со свежими задачами."""
        # Добавляем свежую задачу (только что захваченную)
        fresh_task = {
            "source_type": 1,
            "source_id": 12345,
            "archive_path": "/test/12345.zip",
            "book_filename": "12345.fb2",
            "_claimed_at": time.time(),  # Только что захвачена
        }

        self.fake_redis.lpush(settings.QUEUE_PARSING_PROCESSING, json.dumps(fresh_task))

        stale_tasks = self.auditor.find_stale_tasks()
        self.assertEqual(len(stale_tasks), 0)

    def test_find_stale_tasks_with_stale_tasks(self):
        """Тест поиска зависших задач с устаревшими задачами."""
        # Добавляем зависшую задачу (захвачена час назад)
        stale_task = {
            "source_type": 1,
            "source_id": 12345,
            "archive_path": "/test/12345.zip",
            "book_filename": "12345.fb2",
            "_claimed_at": time.time() - 3600,  # Час назад
        }

        self.fake_redis.lpush(settings.QUEUE_PARSING_PROCESSING, json.dumps(stale_task))

        stale_tasks = self.auditor.find_stale_tasks()
        self.assertEqual(len(stale_tasks), 1)
        self.assertEqual(stale_tasks[0]["source_id"], 12345)
        self.assertIn("_age_seconds", stale_tasks[0])

    def test_recover_stale_task_dry_run(self):
        """Тест восстановления зависшей задачи в режиме dry-run."""
        stale_task = {
            "source_type": 1,
            "source_id": 12345,
            "archive_path": "/test/12345.zip",
            "book_filename": "12345.fb2",
            "_claimed_at": time.time() - 3600,
            "_raw_task": '{"source_type": 1, "source_id": 12345}',
            "_age_seconds": 3600,
        }

        # Убеждаемся, что блокировки нет
        lock_key = "lock:book:1:12345"
        self.assertFalse(self.fake_redis.exists(lock_key))

        stats = self.auditor.recover([stale_task], dry_run=True)

        # В dry-run режиме ничего не должно измениться
        self.assertEqual(stats["recovered"], 1)
        self.assertEqual(stats["skipped_still_locked"], 0)
        self.assertEqual(stats["errors"], 0)

        # Задача должна остаться в processing
        self.assertEqual(self.fake_redis.llen(settings.QUEUE_PARSING_PROCESSING), 0)
        self.assertEqual(self.fake_redis.llen(settings.QUEUE_PARSING_NEW), 0)

    def test_recover_stale_task_fix_mode(self):
        """Тест восстановления зависшей задачи в режиме fix."""
        stale_task = {
            "source_type": 1,
            "source_id": 12345,
            "archive_path": "/test/12345.zip",
            "book_filename": "12345.fb2",
            "_claimed_at": time.time() - 3600,
            "_raw_task": json.dumps(
                {"source_type": 1, "source_id": 12345, "archive_path": "/test/12345.zip", "book_filename": "12345.fb2"}
            ),
            "_age_seconds": 3600,
        }

        # Добавляем задачу в processing
        raw_task_str = str(stale_task["_raw_task"])
        self.fake_redis.lpush(settings.QUEUE_PARSING_PROCESSING, raw_task_str)

        stats = self.auditor.recover([stale_task], dry_run=False)

        self.assertEqual(stats["recovered"], 1)
        self.assertEqual(stats["skipped_still_locked"], 0)
        self.assertEqual(stats["errors"], 0)

        # Задача должна быть перемещена из processing в new
        self.assertEqual(self.fake_redis.llen(settings.QUEUE_PARSING_PROCESSING), 0)
        self.assertEqual(self.fake_redis.llen(settings.QUEUE_PARSING_NEW), 1)

        # Проверяем содержимое задачи в new
        new_tasks_result = self.fake_redis.lrange(settings.QUEUE_PARSING_NEW, 0, -1)
        new_tasks = new_tasks_result if isinstance(new_tasks_result, list) else []
        self.assertTrue(len(new_tasks) > 0, "Задача должна быть в очереди new")
        new_task_raw = new_tasks[0]
        new_task = json.loads(new_task_raw.decode("utf-8"))
        self.assertEqual(new_task["source_id"], 12345)
        self.assertNotIn("_claimed_at", new_task)  # Служебные поля удалены

    def test_recover_locked_task(self):
        """Тест пропуска заблокированной задачи."""
        stale_task = {
            "source_type": 1,
            "source_id": 12345,
            "archive_path": "/test/12345.zip",
            "book_filename": "12345.fb2",
            "_claimed_at": time.time() - 3600,
            "_raw_task": '{"source_type": 1, "source_id": 12345}',
            "_age_seconds": 3600,
        }

        # Устанавливаем блокировку
        lock_key = "lock:book:1:12345"
        self.fake_redis.set(lock_key, "worker_123", ex=300)

        stats = self.auditor.recover([stale_task], dry_run=False)

        self.assertEqual(stats["recovered"], 0)
        self.assertEqual(stats["skipped_still_locked"], 1)
        self.assertEqual(stats["errors"], 0)


class MockDBConnection:
    """Мок для PostgreSQL соединения, совместимый с SQLite."""

    def __init__(self, sqlite_conn):
        self.sqlite_conn = sqlite_conn
        self._in_transaction = False

    def cursor(self):
        return MockCursor(self.sqlite_conn)

    def commit(self):
        self.sqlite_conn.commit()

    def rollback(self):
        self.sqlite_conn.rollback()

    def close(self):
        self.sqlite_conn.close()


class MockCursor:
    """Мок для PostgreSQL курсора, совместимый с SQLite."""

    def __init__(self, sqlite_conn):
        self.sqlite_conn = sqlite_conn
        self.cursor = sqlite_conn.cursor()
        self.rowcount = 0

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        _ = exc_type, exc_val, exc_tb  # Подавляем предупреждения о неиспользуемых переменных
        self.cursor.close()

    def execute(self, query, params=None):
        # Адаптируем PostgreSQL запросы для SQLite (заменяем %s на ?)
        sqlite_query = query.replace("%s", "?")

        if params:
            result = self.cursor.execute(sqlite_query, params)
        else:
            result = self.cursor.execute(sqlite_query)
        self.rowcount = self.cursor.rowcount
        return result

    def fetchall(self):
        return self.cursor.fetchall()


class TestIncompleteBookAuditor(unittest.TestCase):
    """Тесты для аудитора незавершенных книг."""

    def setUp(self):
        """Настройка тестового окружения."""
        # Создаем временную SQLite БД
        self.db_file = tempfile.NamedTemporaryFile(delete=False)
        self.db_file.close()

        sqlite_conn = sqlite3.connect(self.db_file.name)
        sqlite_conn.row_factory = sqlite3.Row

        # Создаем упрощенную схему БД
        sqlite_conn.execute("""
            CREATE TABLE books (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                process_status INTEGER DEFAULT 0,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        sqlite_conn.execute("""
            CREATE TABLE book_sources (
                id INTEGER PRIMARY KEY,
                book_id TEXT,
                source_type INTEGER,
                source_id INTEGER,
                FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
            )
        """)

        # Оборачиваем в мок для совместимости с PostgreSQL API
        self.db_connection = MockDBConnection(sqlite_conn)

        self.fake_redis = fakeredis.FakeRedis()
        self.logger = logging.getLogger("test")

        # Мокаем StorageManager
        self.storage_manager = Mock()

        self.auditor = IncompleteBookAuditor(self.db_connection, self.fake_redis, self.storage_manager, self.logger)  # type: ignore

        # Создаем временную директорию для артефактов
        self.temp_dir = tempfile.mkdtemp()
        self.artifact_dir = Path(self.temp_dir)

    def tearDown(self):
        """Очистка после тестов."""
        self.db_connection.close()
        Path(self.db_file.name).unlink()
        self.fake_redis.flushall()

        # Очищаем временную директорию
        import shutil

        shutil.rmtree(self.temp_dir)

    def test_find_incomplete_books_empty(self):
        """Тест поиска незавершенных книг в пустой БД."""
        incomplete_books = self.auditor.find_incomplete_books()
        self.assertEqual(len(incomplete_books), 0)

    def test_find_incomplete_books_with_status_10(self):
        """Тест поиска книг со статусом 10."""
        book_id = str(uuid4())

        # Добавляем книгу со статусом 10
        with self.db_connection.cursor() as cursor:
            cursor.execute(
                "INSERT INTO books (id, title, process_status) VALUES (?, ?, ?)", (book_id, "Тестовая книга", 10)
            )

            cursor.execute(
                "INSERT INTO book_sources (book_id, source_type, source_id) VALUES (?, ?, ?)", (book_id, 1, 12345)
            )

        self.db_connection.commit()

        incomplete_books = self.auditor.find_incomplete_books()
        self.assertEqual(len(incomplete_books), 1)
        self.assertEqual(incomplete_books[0]["id"], book_id)
        self.assertEqual(incomplete_books[0]["title"], "Тестовая книга")
        self.assertEqual(incomplete_books[0]["source_type"], 1)
        self.assertEqual(incomplete_books[0]["source_id"], 12345)

    @patch("run_00_recovery._get_artifact_path")
    def test_recover_book_with_artifact_dry_run(self, mock_get_artifact_path):
        """Тест восстановления книги с существующим артефактом в dry-run."""
        book_id = str(uuid4())

        # Мокаем существующий артефакт
        mock_artifact_path = Mock()
        mock_artifact_path.exists.return_value = True
        mock_artifact_path.stat.return_value.st_size = 1024
        mock_get_artifact_path.return_value = mock_artifact_path

        book = {"id": book_id, "title": "Тестовая книга", "source_type": 1, "source_id": 12345}

        stats = self.auditor.recover([book], dry_run=True)

        self.assertEqual(stats["status_updated"], 1)
        self.assertEqual(stats["rolled_back"], 0)
        self.assertEqual(stats["errors"], 0)

    @patch("run_00_recovery._get_artifact_path")
    def test_recover_book_without_artifact_dry_run(self, mock_get_artifact_path):
        """Тест восстановления книги без артефакта в dry-run."""
        book_id = str(uuid4())

        # Мокаем отсутствующий артефакт
        mock_artifact_path = Mock()
        mock_artifact_path.exists.return_value = False
        mock_get_artifact_path.return_value = mock_artifact_path

        book = {"id": book_id, "title": "Тестовая книга", "source_type": 1, "source_id": 12345}

        stats = self.auditor.recover([book], dry_run=True)

        self.assertEqual(stats["status_updated"], 0)
        self.assertEqual(stats["rolled_back"], 1)
        self.assertEqual(stats["errors"], 0)

    @patch("run_00_recovery._get_artifact_path")
    def test_recover_book_without_artifact_fix_mode(self, mock_get_artifact_path):
        """Тест полного отката книги без артефакта в режиме fix."""
        book_id = str(uuid4())

        # Добавляем книгу в БД
        with self.db_connection.cursor() as cursor:
            cursor.execute(
                "INSERT INTO books (id, title, process_status) VALUES (?, ?, ?)", (book_id, "Тестовая книга", 10)
            )
            cursor.execute(
                "INSERT INTO book_sources (book_id, source_type, source_id) VALUES (?, ?, ?)", (book_id, 1, 12345)
            )
        self.db_connection.commit()

        # Добавляем в Redis SET
        self.fake_redis.sadd(settings.SET_QUEUED_IDS, "1:12345")

        # Мокаем отсутствующий артефакт
        mock_artifact_path = Mock()
        mock_artifact_path.exists.return_value = False
        mock_get_artifact_path.return_value = mock_artifact_path

        book = {"id": book_id, "title": "Тестовая книга", "source_type": 1, "source_id": 12345}

        stats = self.auditor.recover([book], dry_run=False)

        self.assertEqual(stats["status_updated"], 0)
        self.assertEqual(stats["rolled_back"], 1)
        self.assertEqual(stats["errors"], 0)

        # Проверяем, что книга удалена из БД
        with self.db_connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM books WHERE id = ?", (book_id,))
            count = cursor.fetchall()[0][0]
            self.assertEqual(count, 0)

        # Проверяем, что ключ удален из Redis
        self.assertFalse(self.fake_redis.sismember(settings.SET_QUEUED_IDS, "1:12345"))


class TestSystemRecoveryIntegration(unittest.TestCase):
    """Интеграционные тесты для SystemRecovery."""

    @patch("run_00_recovery.psycopg.connect")
    @patch("run_00_recovery.redis.from_url")
    @patch("run_00_recovery.LocalStorageManager")
    def test_system_recovery_dry_run_no_issues(self, mock_storage, mock_redis, mock_psycopg):
        """Тест SystemRecovery в dry-run режиме без проблем."""
        _ = mock_storage  # Подавляем предупреждение о неиспользуемой переменной
        # Мокаем зависимости
        mock_redis_client = fakeredis.FakeRedis()
        mock_redis.return_value = mock_redis_client

        mock_db = MockDBConnection(sqlite3.connect(":memory:"))
        mock_psycopg.return_value = mock_db

        # Создаем схему БД
        with mock_db.cursor() as cursor:
            cursor.execute("""
                CREATE TABLE books (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    process_status INTEGER DEFAULT 0,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            cursor.execute("""
                CREATE TABLE book_sources (
                    id INTEGER PRIMARY KEY,
                    book_id TEXT,
                    source_type INTEGER,
                    source_id INTEGER
                )
            """)
        mock_db.commit()

        # Создаем SystemRecovery и запускаем
        with patch("builtins.print"):  # Подавляем вывод отчета
            recovery = SystemRecovery(verbose=False, debug=False)
            recovery.run(dry_run=True)

        # Проверяем, что соединения были закрыты
        # (в реальности это сложно проверить, но мы можем убедиться, что метод завершился без ошибок)


if __name__ == "__main__":
    # Настраиваем логирование для тестов
    logging.basicConfig(level=logging.WARNING)

    # Запускаем тесты
    unittest.main(verbosity=2)
